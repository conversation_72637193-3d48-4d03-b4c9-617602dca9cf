<template>
  <div class="query-builder">
    <div class="page-header">
      <h1>查询构建器</h1>
      <div class="header-actions">
        <el-button @click="resetQuery">
          <el-icon>
            <Refresh />
          </el-icon>
          重置
        </el-button>
        <el-button @click="saveQuery" :disabled="!isQueryValid">
          <el-icon>
            <Document />
          </el-icon>
          保存查询
        </el-button>
      </div>
    </div>

    <div class="builder-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-card shadow="never" class="config-card">
          <template #header>
            <div class="card-header">
              <span>查询配置</span>
              <el-steps :active="queryStore.currentStep" style="width: 85%;" :space="200" simple>
                <el-step title="选择表" />
                <el-step title="选择字段" />
                <el-step title="配置过滤" />
                <el-step title="设置变量" />
              </el-steps>
            </div>
          </template>

          <div class="config-content">
            <!-- 步骤1: 表选择 -->
            <div v-show="queryStore.currentStep === 0" class="step-content">
              <TableSelector v-model="queryStore.currentQuery.tableName" @change="handleTableChange" />
            </div>

            <!-- 步骤2: 字段选择 -->
            <div v-show="queryStore.currentStep === 1" class="step-content">
              <div class="field-selection">
                <!-- 维度选择 -->
                <div class="dimension-section">
                  <h4>选择维度</h4>
                  <DimensionSelector v-model="queryStore.currentQuery.dimensions"
                    :table-name="queryStore.currentQuery.tableName" @change="handleDimensionChange" />
                </div>

                <!-- 指标选择 -->
                <div class="metric-section">
                  <h4>选择指标</h4>
                  <MetricSelector v-model="queryStore.currentQuery.metrics"
                    :table-name="queryStore.currentQuery.tableName" @change="handleMetricChange" />
                </div>
              </div>
            </div>

            <!-- 步骤3: 过滤配置 -->
            <div v-show="queryStore.currentStep === 2" class="step-content">
              <FilterConfig v-model="queryStore.currentQuery.filters" :table-name="queryStore.currentQuery.tableName"
                @change="handleFilterChange" />
            </div>

            <!-- 步骤4: 变量设置 -->
            <div v-show="queryStore.currentStep === 3" class="step-content">
              <VariableInput v-model="queryStore.currentQuery.variables" @change="handleVariableChange" />
            </div>
          </div>

          <div class="step-actions">
            <el-button v-if="queryStore.currentStep > 0" @click="prevStep">
              上一步
            </el-button>
            <el-button v-if="queryStore.currentStep < 3" type="primary" :disabled="!canNextStep" @click="nextStep">
              下一步
            </el-button>
            <el-button v-if="queryStore.currentStep === 3" type="success" :disabled="!isQueryValid"
              @click="generateQuery">
              生成查询
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel">
        <el-card shadow="never" class="preview-card">
          <template #header>
            <div class="card-header">
              <span>SQL预览</span>
              <el-tag v-if="isQueryValid" type="success" size="small">
                查询就绪
              </el-tag>
              <el-tag v-else type="info" size="small">
                配置中
              </el-tag>
            </div>
          </template>

          <SqlPreview :query-config="queryStore.currentQuery" @execute="handleExecuteQuery" />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Document } from '@element-plus/icons-vue'
import { useQueryStore } from '@/stores/query'
import TableSelector from '@/components/query/TableSelector.vue'
import DimensionSelector from '@/components/query/DimensionSelector.vue'
import MetricSelector from '@/components/query/MetricSelector.vue'
import FilterConfig from '@/components/query/FilterConfig.vue'
import VariableInput from '@/components/query/VariableInput.vue'
import SqlPreview from '@/components/query/SqlPreview.vue'

// 使用store
const queryStore = useQueryStore()

// 计算属性
const isQueryValid = computed(() => queryStore.isQueryValid)
const canNextStep = computed(() => {
  switch (queryStore.currentStep) {
    case 0:
      return !!queryStore.currentQuery.tableName
    case 1:
      return queryStore.currentQuery.dimensions.length > 0 || queryStore.currentQuery.metrics.length > 0
    case 2:
      return true // 过滤条件是可选的
    case 3:
      return true // 变量是可选的
    default:
      return false
  }
})

// 事件处理函数
const handleTableChange = (tableName: string) => {
  queryStore.updateQueryConfig({ tableName })
  // 清空之前的选择
  queryStore.clearSelections()
}

const handleDimensionChange = (dimensions: string[]) => {
  queryStore.currentQuery.dimensions = dimensions
}

const handleMetricChange = (metrics: any[]) => {
  queryStore.currentQuery.metrics = metrics
}

const handleFilterChange = (filters: any[]) => {
  queryStore.currentQuery.filters = filters
}

const handleVariableChange = (variables: any[]) => {
  queryStore.currentQuery.variables = variables
}

const handleExecuteQuery = async (sql: string) => {
  try {
    await queryStore.buildQuery()
    ElMessage.success('查询已提交执行')
  } catch (error) {
    ElMessage.error('查询执行失败')
  }
}

// 步骤控制
const nextStep = () => {
  if (queryStore.currentStep < 3) {
    queryStore.setCurrentStep(queryStore.currentStep + 1)
  }
}

const prevStep = () => {
  if (queryStore.currentStep > 0) {
    queryStore.setCurrentStep(queryStore.currentStep - 1)
  }
}

// 重置查询
const resetQuery = () => {
  queryStore.resetQuery()
  ElMessage.success('查询已重置')
}

// 保存查询
const saveQuery = () => {
  if (!isQueryValid.value) {
    ElMessage.warning('请完成查询配置')
    return
  }

  const name = `查询_${new Date().toLocaleString()}`
  queryStore.saveQuery(name)
  ElMessage.success('查询已保存')
}

// 生成查询
const generateQuery = async () => {
  if (!isQueryValid.value) {
    ElMessage.warning('请完成查询配置')
    return
  }

  try {
    await queryStore.buildQuery()
    ElMessage.success('查询已生成，请查看右侧预览')
  } catch (error) {
    ElMessage.error('生成查询失败')
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.query-builder {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      color: $text-primary;
    }

    .header-actions {
      display: flex;
      gap: $spacing-sm;
    }
  }

  .builder-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-lg;
    height: calc(100vh - 200px);

    .config-panel {
      .config-card {
        height: 100%;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-weight: 500;
            color: $text-primary;
          }
        }

        .config-content {
          min-height: 400px;

          .step-content {
            .field-selection {

              .dimension-section,
              .metric-section {
                margin-bottom: $spacing-lg;

                h4 {
                  margin: 0 0 $spacing-md 0;
                  color: $text-primary;
                  font-size: $font-size-md;
                }
              }
            }
          }
        }

        .step-actions {
          display: flex;
          justify-content: flex-end;
          gap: $spacing-sm;
          margin-top: $spacing-lg;
          padding-top: $spacing-md;
          border-top: 1px solid $border-color;
        }
      }
    }

    .preview-panel {
      .preview-card {
        height: 100%;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-weight: 500;
            color: $text-primary;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .query-builder {
    .builder-content {
      grid-template-columns: 1fr;
      height: auto;

      .config-panel,
      .preview-panel {

        .config-card,
        .preview-card {
          height: auto;
        }
      }
    }
  }
}
</style>
