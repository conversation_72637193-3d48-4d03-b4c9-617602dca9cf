import { defineStore } from 'pinia';
import { queryApi } from '@/api/query';
import type {
  QueryConfig,
  MetricSelection,
  VariableDefinition,
  DependencyNode,
  QueryHistory,
  VariableInfo,
  MetricDependency,
  FilterCondition,
  VariableConfig
} from '@/types/query';

interface QueryState {
  // 当前查询配置
  currentQuery: QueryConfig;

  // 生成的SQL
  generatedSql: string;

  // 查询历史
  queryHistory: QueryHistory[];

  // 依赖关系
  metricDependencies: MetricDependency[];

  // 必需变量
  requiredVariables: VariableInfo[];

  // 当前步骤
  currentStep: number;

  // 预览标签页
  previewTab: string;

  // 状态
  loading: boolean;
  error: string | null;
}

export const useQueryStore = defineStore('query', {
  state: (): QueryState => ({
    currentQuery: {
      tableName: '',
      metrics: [],
      dimensions: [],
      filters: [],
      variables: []
    },

    generatedSql: '',
    queryHistory: [],
    metricDependencies: [],
    requiredVariables: [],
    currentStep: 0,
    previewTab: 'sql',
    loading: false,
    error: null
  }),

  getters: {
    isQueryValid: (state) => {
      return state.currentQuery.tableName && 
             state.currentQuery.metrics.length > 0;
    },
    
    selectedMetricNames: (state) => {
      return state.currentQuery.metrics.map(m => m.name);
    },
    
    hasRequiredVariables: (state) => {
      return state.requiredVariables.length > 0;
    },
    
    getQueryById: (state) => {
      return (id: string) => state.queryHistory.find(q => q.id === id);
    }
  },

  actions: {
    // 分析查询需求
    async analyzeQuery() {
      try {
        this.loading = true;
        this.error = null;

        if (!this.isQueryValid) {
          throw new Error('查询配置不完整');
        }

        const response = await queryApi.analyze({
          tableName: this.currentQuery.tableName,
          metrics: this.selectedMetricNames,
          dimensions: this.currentQuery.dimensions,
          filters: this.currentQuery.filters
        });

        this.requiredVariables = response.requiredVariables;
        this.metricDependencies = response.dependencies;

      } catch (error) {
        this.error = error instanceof Error ? error.message : '分析查询失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 构建SQL查询
    async buildQuery() {
      try {
        this.loading = true;
        this.error = null;

        if (!this.isQueryValid) {
          throw new Error('查询配置不完整');
        }

        // 准备变量数据
        const variables: Record<string, any> = {};
        this.currentQuery.variables.forEach(v => {
          variables[v.name] = v.value;
        });

        const response = await queryApi.build({
          tableName: this.currentQuery.tableName,
          metrics: this.selectedMetricNames,
          dimensions: this.currentQuery.dimensions,
          filters: this.currentQuery.filters,
          variables
        });

        this.generatedSql = response.sql;

      } catch (error) {
        this.error = error instanceof Error ? error.message : '构建查询失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 预览查询
    async previewQuery() {
      try {
        this.loading = true;
        this.error = null;

        if (!this.isQueryValid) {
          throw new Error('查询配置不完整');
        }

        const response = await queryApi.preview({
          tableName: this.currentQuery.tableName,
          metrics: this.selectedMetricNames,
          dimensions: this.currentQuery.dimensions,
          filters: this.currentQuery.filters
        });

        this.generatedSql = response.sql;

      } catch (error) {
        this.error = error instanceof Error ? error.message : '预览查询失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 验证SQL
    async validateSql(sql: string) {
      try {
        this.loading = true;
        this.error = null;

        const response = await queryApi.validate({ sql });
        return response;

      } catch (error) {
        this.error = error instanceof Error ? error.message : '验证SQL失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 保存查询
    saveQuery(name: string) {
      const query: QueryHistory = {
        id: Date.now().toString(),
        name,
        config: { ...this.currentQuery },
        sql: this.generatedSql,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.queryHistory.unshift(query);

      // 限制历史记录数量
      if (this.queryHistory.length > 50) {
        this.queryHistory = this.queryHistory.slice(0, 50);
      }

      return query;
    },

    // 加载查询
    loadQuery(query: QueryHistory) {
      this.currentQuery = { ...query.config };
      this.generatedSql = query.sql;
    },

    // 重置查询
    resetQuery() {
      this.currentQuery = {
        tableName: '',
        metrics: [],
        dimensions: [],
        filters: [],
        variables: []
      };
      this.generatedSql = '';
      this.requiredVariables = [];
      this.metricDependencies = [];
      this.currentStep = 0;
      this.previewTab = 'sql';
      this.error = null;
    },

    // 更新查询配置
    updateQueryConfig(config: Partial<QueryConfig>) {
      this.currentQuery = { ...this.currentQuery, ...config };
    },

    // 清空选择（切换表时使用）
    clearSelections() {
      this.currentQuery.dimensions = [];
      this.currentQuery.metrics = [];
      this.currentQuery.filters = [];
      this.currentQuery.variables = [];
    },

    // 设置当前步骤
    setCurrentStep(step: number) {
      this.currentStep = step;
    },

    // 添加指标
    addMetric(metric: MetricSelection) {
      const exists = this.currentQuery.metrics.find(m => m.name === metric.name);
      if (!exists) {
        this.currentQuery.metrics.push(metric);
      }
    },

    // 移除指标
    removeMetric(metricName: string) {
      const index = this.currentQuery.metrics.findIndex(m => m.name === metricName);
      if (index !== -1) {
        this.currentQuery.metrics.splice(index, 1);
      }
    },

    // 添加维度
    addDimension(dimension: string) {
      if (!this.currentQuery.dimensions.includes(dimension)) {
        this.currentQuery.dimensions.push(dimension);
      }
    },

    // 移除维度
    removeDimension(dimension: string) {
      const index = this.currentQuery.dimensions.indexOf(dimension);
      if (index !== -1) {
        this.currentQuery.dimensions.splice(index, 1);
      }
    },

    // 添加过滤条件
    addFilter(filter: FilterCondition) {
      this.currentQuery.filters.push(filter);
    },

    // 更新过滤条件
    updateFilter(filterId: string, updates: Partial<FilterCondition>) {
      const index = this.currentQuery.filters.findIndex(f => f.id === filterId);
      if (index !== -1) {
        this.currentQuery.filters[index] = { ...this.currentQuery.filters[index], ...updates };
      }
    },

    // 移除过滤条件
    removeFilter(filterId: string) {
      const index = this.currentQuery.filters.findIndex(f => f.id === filterId);
      if (index !== -1) {
        this.currentQuery.filters.splice(index, 1);
      }
    },

    // 添加变量
    addVariable(variable: VariableConfig) {
      const exists = this.currentQuery.variables.find(v => v.name === variable.name);
      if (!exists) {
        this.currentQuery.variables.push(variable);
      }
    },

    // 更新变量
    updateVariable(variableName: string, updates: Partial<VariableConfig>) {
      const index = this.currentQuery.variables.findIndex(v => v.name === variableName);
      if (index !== -1) {
        this.currentQuery.variables[index] = { ...this.currentQuery.variables[index], ...updates };
      }
    },

    // 移除变量
    removeVariable(variableName: string) {
      const index = this.currentQuery.variables.findIndex(v => v.name === variableName);
      if (index !== -1) {
        this.currentQuery.variables.splice(index, 1);
      }
    },

    // 设置预览标签页
    setPreviewTab(tab: string) {
      this.previewTab = tab;
    }
  }
});
