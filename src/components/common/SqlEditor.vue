<template>
  <div class="sql-editor">
    <div 
      ref="editorContainer" 
      class="editor-container"
      :style="{ height: height }"
    />

    <div v-if="!readonly && showActions" class="editor-actions">
      <el-button size="small" @click="formatSql">
        <el-icon><MagicStick /></el-icon>
        格式化
      </el-button>
      
      <el-button size="small" @click="validateSql">
        <el-icon><Check /></el-icon>
        验证语法
      </el-button>
      
      <el-button 
        v-if="showExecute"
        size="small" 
        type="primary" 
        @click="executeSql"
      >
        <el-icon><CaretRight /></el-icon>
        执行
      </el-button>
      
      <el-button 
        v-if="showCopy"
        size="small" 
        @click="copySql"
      >
        <el-icon><CopyDocument /></el-icon>
        复制
      </el-button>
    </div>

    <!-- 验证结果 -->
    <div v-if="validationResult" class="validation-result">
      <el-alert
        :type="validationResult.valid ? 'success' : 'error'"
        :title="validationResult.valid ? 'SQL语法正确' : 'SQL语法错误'"
        :description="validationResult.message"
        :closable="true"
        @close="validationResult = null"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as monaco from 'monaco-editor';
import { ElMessage } from 'element-plus';
import { MagicStick, Check, CaretRight, CopyDocument } from '@element-plus/icons-vue';

interface Props {
  modelValue: string;
  readonly?: boolean;
  language?: string;
  theme?: string;
  height?: string;
  showActions?: boolean;
  showExecute?: boolean;
  showCopy?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  language: 'sql',
  theme: 'vs-dark',
  height: '300px',
  showActions: true,
  showExecute: false,
  showCopy: true,
  placeholder: '请输入SQL语句...'
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  execute: [sql: string];
  validate: [sql: string];
}>();

const editorContainer = ref<HTMLElement>();
const validationResult = ref<{
  valid: boolean;
  message: string;
} | null>(null);

let editor: monaco.editor.IStandaloneCodeEditor | null = null;

// 初始化编辑器
const initEditor = async () => {
  if (!editorContainer.value) return;

  // 配置SQL语言
  monaco.languages.setLanguageConfiguration('sql', {
    comments: {
      lineComment: '--',
      blockComment: ['/*', '*/']
    },
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')']
    ],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ],
    surroundingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ]
  });

  // 创建编辑器实例
  editor = monaco.editor.create(editorContainer.value, {
    value: props.modelValue || '',
    language: props.language,
    theme: props.theme,
    readOnly: props.readonly,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineNumbers: 'on',
    roundedSelection: false,
    automaticLayout: true,
    wordWrap: 'on',
    contextmenu: true,
    selectOnLineNumbers: true,
    glyphMargin: false,
    folding: true,
    lineDecorationsWidth: 10,
    lineNumbersMinChars: 3,
    renderLineHighlight: 'line',
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      useShadows: false,
      verticalHasArrows: false,
      horizontalHasArrows: false
    }
  });

  // 监听内容变化
  editor.onDidChangeModelContent(() => {
    if (editor) {
      const value = editor.getValue();
      emit('update:modelValue', value);
    }
  });

  // 设置占位符
  if (props.placeholder && !props.modelValue) {
    showPlaceholder();
  }
};

// 显示占位符
const showPlaceholder = () => {
  if (!editor) return;
  
  editor.setValue('');
  editor.setPosition({ lineNumber: 1, column: 1 });
  
  // 添加占位符装饰
  const decorations = editor.deltaDecorations([], [
    {
      range: new monaco.Range(1, 1, 1, 1),
      options: {
        afterContentClassName: 'placeholder-text',
        isWholeLine: true
      }
    }
  ]);
  
  // 监听焦点事件移除占位符
  const disposable = editor.onDidFocusEditorText(() => {
    editor?.deltaDecorations(decorations, []);
    disposable.dispose();
  });
};

// 格式化SQL
const formatSql = () => {
  if (!editor) return;
  
  editor.getAction('editor.action.formatDocument')?.run();
  ElMessage.success('SQL已格式化');
};

// 验证SQL语法
const validateSql = () => {
  if (!editor) return;
  
  const sql = editor.getValue().trim();
  if (!sql) {
    ElMessage.warning('请输入SQL语句');
    return;
  }
  
  // 简单的SQL语法验证
  const basicSqlPattern = /^\s*(SELECT|INSERT|UPDATE|DELETE|WITH|CREATE|ALTER|DROP)\s+/i;
  const valid = basicSqlPattern.test(sql);
  
  validationResult.value = {
    valid,
    message: valid ? 'SQL语法检查通过' : '请检查SQL语法是否正确'
  };
  
  emit('validate', sql);
};

// 执行SQL
const executeSql = () => {
  if (!editor) return;
  
  const sql = editor.getValue().trim();
  if (!sql) {
    ElMessage.warning('请输入SQL语句');
    return;
  }
  
  emit('execute', sql);
};

// 复制SQL
const copySql = async () => {
  if (!editor) return;
  
  const sql = editor.getValue();
  if (!sql.trim()) {
    ElMessage.warning('没有可复制的内容');
    return;
  }
  
  try {
    await navigator.clipboard.writeText(sql);
    ElMessage.success('SQL已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
};

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    editor.setValue(newValue || '');
  }
});

// 监听主题变化
watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme);
  }
});

onMounted(async () => {
  await nextTick();
  initEditor();
});

onUnmounted(() => {
  if (editor) {
    editor.dispose();
    editor = null;
  }
});
</script>

<style lang="scss" scoped>
.sql-editor {
  .editor-container {
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    overflow: hidden;
    
    :deep(.monaco-editor) {
      .placeholder-text::after {
        content: v-bind(placeholder);
        color: $text-placeholder;
        font-style: italic;
        pointer-events: none;
      }
    }
  }
  
  .editor-actions {
    margin-top: $spacing-sm;
    display: flex;
    justify-content: flex-end;
    gap: $spacing-xs;
    
    .el-button {
      .el-icon {
        margin-right: $spacing-xs;
      }
    }
  }
  
  .validation-result {
    margin-top: $spacing-sm;
  }
}

@media (max-width: $breakpoint-md) {
  .sql-editor {
    .editor-actions {
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}
</style>
