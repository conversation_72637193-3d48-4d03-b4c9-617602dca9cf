<template>
  <div class="app-footer-content">
    <div class="footer-left">
      <span>© 2024 Query Builder - ClickHouse查询管理工具</span>
    </div>
    
    <div class="footer-center">
      <el-link
        v-if="systemInfo.status === 'healthy'"
        type="success"
        :underline="false"
        @click="showApiDocs"
      >
        <el-icon><Link /></el-icon>
        API文档
      </el-link>
    </div>
    
    <div class="footer-right">
      <span>版本 {{ systemInfo.version }}</span>
      <el-divider direction="vertical" />
      <span>
        状态: 
        <el-tag
          :type="systemInfo.status === 'healthy' ? 'success' : 'danger'"
          size="small"
          effect="plain"
        >
          {{ systemInfo.status === 'healthy' ? '正常' : '异常' }}
        </el-tag>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/stores';
import { ElMessage } from 'element-plus';
import { Link } from '@element-plus/icons-vue';

const appStore = useAppStore();

// 系统信息
const systemInfo = computed(() => appStore.systemInfo);

// 显示API文档
const showApiDocs = () => {
  // 在新窗口打开API文档
  window.open('http://localhost:8000/docs', '_blank');
};
</script>

<style lang="scss" scoped>
.app-footer-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .footer-left {
    font-size: $font-size-xs;
    color: $text-secondary;
  }
  
  .footer-center {
    .el-link {
      font-size: $font-size-xs;
      
      .el-icon {
        margin-right: $spacing-xs;
      }
    }
  }
  
  .footer-right {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-size: $font-size-xs;
    color: $text-secondary;
    
    .el-tag {
      font-size: $font-size-xs;
    }
  }
}

@media (max-width: $breakpoint-md) {
  .app-footer-content {
    .footer-left {
      display: none;
    }
    
    .footer-center {
      flex: 1;
    }
  }
}
</style>
