<template>
  <div class="app-sidebar-content">
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <!-- 仪表板 -->
      <el-menu-item index="/dashboard">
        <el-icon><Monitor /></el-icon>
        <template #title>仪表板</template>
      </el-menu-item>

      <!-- 数据管理 -->
      <el-sub-menu index="data">
        <template #title>
          <el-icon><Grid /></el-icon>
          <span>数据管理</span>
        </template>
        
        <el-menu-item index="/variables">
          <el-icon><Setting /></el-icon>
          <template #title>变量管理</template>
        </el-menu-item>
        
        <el-menu-item index="/tables">
          <el-icon><Document /></el-icon>
          <template #title>表管理</template>
        </el-menu-item>
      </el-sub-menu>

      <!-- 查询工具 -->
      <el-sub-menu index="query">
        <template #title>
          <el-icon><Search /></el-icon>
          <span>查询工具</span>
        </template>
        
        <el-menu-item index="/query-builder">
          <el-icon><Tools /></el-icon>
          <template #title>查询构建器</template>
        </el-menu-item>
        
        <el-menu-item index="/sql-preview">
          <el-icon><View /></el-icon>
          <template #title>SQL预览</template>
        </el-menu-item>
      </el-sub-menu>

      <!-- API测试 -->
      <el-menu-item index="/api-test">
        <el-icon><Tools /></el-icon>
        <template #title>API测试</template>
      </el-menu-item>
    </el-menu>

    <!-- 底部信息 -->
    <div v-if="!collapsed" class="sidebar-footer">
      <div class="system-stats">
        <div class="stat-item">
          <span class="stat-label">数据表</span>
          <span class="stat-value">{{ systemSummary?.tableCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">变量</span>
          <span class="stat-value">{{ systemSummary?.variableCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">指标</span>
          <span class="stat-value">{{ systemSummary?.metricCount || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores';
import { Monitor, Grid, Setting, Document, Search, Tools, View } from '@element-plus/icons-vue';

const route = useRoute();
const appStore = useAppStore();

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path;
  
  // 处理表详情页面的特殊情况
  if (path.startsWith('/tables/')) {
    return '/tables';
  }
  
  return path;
});

// 侧边栏是否折叠
const collapsed = computed(() => appStore.sidebarCollapsed);

// 系统统计信息
const systemSummary = computed(() => appStore.systemSummary);

// 组件挂载时获取系统统计
onMounted(() => {
  appStore.fetchSystemSummary();
});
</script>

<style lang="scss" scoped>
.app-sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  
  .el-menu-item {
    height: 48px;
    line-height: 48px;
    
    &.is-active {
      background-color: lighten($primary-color, 45%);
      color: $primary-color;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: $primary-color;
      }
    }
    
    &:hover {
      background-color: lighten($primary-color, 48%);
    }
  }
  
  .el-sub-menu {
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background-color: lighten($primary-color, 48%);
      }
    }
  }
  
  .el-icon {
    margin-right: $spacing-sm;
    font-size: 16px;
  }
}

.sidebar-footer {
  padding: $spacing-md;
  border-top: 1px solid $border-color;
  background-color: $bg-color-light;
  
  .system-stats {
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-xs;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .stat-label {
        font-size: $font-size-xs;
        color: $text-secondary;
      }
      
      .stat-value {
        font-size: $font-size-sm;
        font-weight: 600;
        color: $primary-color;
      }
    }
  }
}

// 折叠状态下的样式调整
.sidebar-menu.el-menu--collapse {
  .sidebar-footer {
    display: none;
  }
}
</style>
