<template>
  <div class="app-header-content">
    <!-- 左侧：Logo和标题 -->
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon><Menu /></el-icon>
      </el-button>
      
      <div class="logo-section">
        <el-icon class="logo-icon"><Grid /></el-icon>
        <h1 class="app-title">Query Builder</h1>
      </div>
    </div>

    <!-- 中间：面包屑导航 -->
    <div class="header-center">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="item in breadcrumbs"
          :key="item.path || item.name"
          :to="item.path ? { path: item.path } : undefined"
        >
          {{ item.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧：操作按钮 -->
    <div class="header-right">
      <!-- 系统状态 -->
      <el-tooltip content="系统状态" placement="bottom">
        <el-badge
          :value="systemInfo.status === 'healthy' ? '' : '!'"
          :type="systemInfo.status === 'healthy' ? 'success' : 'danger'"
          :hidden="systemInfo.status === 'healthy'"
        >
          <el-button
            type="text"
            :class="['status-indicator', systemInfo.status]"
            @click="showSystemInfo"
          >
            <el-icon><Monitor /></el-icon>
          </el-button>
        </el-badge>
      </el-tooltip>

      <!-- 设置 -->
      <el-dropdown @command="handleCommand">
        <el-button type="text">
          <el-icon><Setting /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="theme">
              <el-icon><Sunny /></el-icon>
              切换主题
            </el-dropdown-item>
            <el-dropdown-item command="language">
              <el-icon><Location /></el-icon>
              语言设置
            </el-dropdown-item>
            <el-dropdown-item divided command="about">
              <el-icon><InfoFilled /></el-icon>
              关于
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>

  <!-- 系统信息对话框 -->
  <el-dialog
    v-model="systemDialogVisible"
    title="系统信息"
    width="500px"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="版本">
        {{ systemInfo.version }}
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag
          :type="systemInfo.status === 'healthy' ? 'success' : 'danger'"
        >
          {{ systemInfo.status === 'healthy' ? '正常' : '异常' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="数据库路径">
        {{ systemInfo.dbPath }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores';
import { ElMessage } from 'element-plus';
import { Menu, Grid, Monitor, Setting, Sunny, Location, InfoFilled } from '@element-plus/icons-vue';
import type { BreadcrumbItem } from '@/types/common';

const route = useRoute();
const appStore = useAppStore();

const systemDialogVisible = ref(false);

// 计算面包屑
const breadcrumbs = computed((): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [];
  
  // 根据当前路由生成面包屑
  if (route.name === 'Dashboard') {
    items.push({ name: '仪表板' });
  } else if (route.name === 'Variables') {
    items.push({ name: '变量管理' });
  } else if (route.name === 'Tables') {
    items.push({ name: '表管理' });
  } else if (route.name === 'TableDetail') {
    items.push({ path: '/tables', name: '表管理' });
    items.push({ name: route.params.tableName as string });
  } else if (route.name === 'QueryBuilder') {
    items.push({ name: '查询构建器' });
  } else if (route.name === 'SqlPreview') {
    items.push({ name: 'SQL预览' });
  }
  
  return items;
});

// 系统信息
const systemInfo = computed(() => appStore.systemInfo);

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar();
};

// 显示系统信息
const showSystemInfo = () => {
  systemDialogVisible.value = true;
};

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'theme':
      ElMessage.info('主题切换功能开发中...');
      break;
    case 'language':
      ElMessage.info('语言设置功能开发中...');
      break;
    case 'about':
      ElMessage.info('Query Builder v1.0.0 - ClickHouse查询管理工具');
      break;
  }
};

// 初始化时获取系统信息
appStore.fetchSystemInfo();
</script>

<style lang="scss" scoped>
.app-header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $spacing-lg;
}

.header-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  
  .sidebar-toggle {
    font-size: 18px;
    color: $text-regular;
    
    &:hover {
      color: $primary-color;
    }
  }
  
  .logo-section {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    
    .logo-icon {
      font-size: 24px;
      color: $primary-color;
    }
    
    .app-title {
      margin: 0;
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }
  }
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  
  .el-breadcrumb {
    font-size: $font-size-sm;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  
  .status-indicator {
    font-size: 18px;
    
    &.healthy {
      color: $success-color;
    }
    
    &.error {
      color: $danger-color;
    }
    
    &.unknown {
      color: $warning-color;
    }
  }
  
  .el-button {
    font-size: 16px;
    color: $text-regular;
    
    &:hover {
      color: $primary-color;
    }
  }
}

@media (max-width: $breakpoint-md) {
  .header-center {
    display: none;
  }
  
  .header-left {
    .app-title {
      display: none;
    }
  }
}
</style>
