<template>
  <div class="dimension-selector">
    <div v-if="!tableName" class="no-table-hint">
      <el-alert
        title="请先选择数据表"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else>
      <!-- 搜索框 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索维度..."
          clearable
          :prefix-icon="Search"
        />
      </div>

      <!-- 维度列表 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="filteredDimensions.length === 0" class="empty-container">
        <el-empty description="暂无维度数据" />
      </div>

      <div v-else class="dimensions-container">
        <div
          v-for="dimension in filteredDimensions"
          :key="dimension.name"
          class="dimension-item"
          :class="{ selected: isSelected(dimension.name) }"
          @click="handleDimensionClick(dimension.name)"
        >
          <div class="dimension-header">
            <div class="dimension-name">
              <el-checkbox
                :model-value="isSelected(dimension.name)"
                @change="handleDimensionClick(dimension.name)"
                @click.stop
              />
              <span class="name">{{ dimension.name }}</span>
              <el-tag
                v-if="dimension.dataType"
                size="small"
                type="info"
              >
                {{ dimension.dataType }}
              </el-tag>
            </div>
          </div>

          <div v-if="dimension.description" class="dimension-description">
            {{ dimension.description }}
          </div>

          <div class="dimension-info">
            <span v-if="dimension.dataType" class="data-type">
              类型: {{ dimension.dataType }}
            </span>
            <span v-if="dimension.nullable !== undefined" class="nullable">
              {{ dimension.nullable ? '可空' : '非空' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 已选维度 -->
      <div v-if="selectedDimensions.length > 0" class="selected-dimensions">
        <div class="section-title">已选维度 ({{ selectedDimensions.length }})</div>
        <div class="dimension-tags">
          <el-tag
            v-for="dimension in selectedDimensions"
            :key="dimension"
            type="warning"
            closable
            @close="handleDimensionDeselect(dimension)"
          >
            {{ dimension }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useDimensionsStore } from '@/stores/dimensions'
import type { Dimension } from '@/types/api'

interface Props {
  modelValue: string[]
  tableName?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dimensionsStore = useDimensionsStore()

const searchKeyword = ref('')
const loading = ref(false)

// 计算属性
const dimensions = computed(() => dimensionsStore.dimensions)

const filteredDimensions = computed(() => {
  if (!searchKeyword.value) return dimensions.value
  return dimensions.value.filter(dimension => 
    dimension.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (dimension.description && dimension.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

const selectedDimensions = computed({
  get: () => Array.isArray(props.modelValue) ? props.modelValue : [],
  set: (value) => emit('update:modelValue', value)
})

// 监听表名变化
watch(() => props.tableName, async (newTableName) => {
  if (newTableName) {
    await loadDimensions(newTableName)
  } else {
    // 清空维度数据
    dimensionsStore.clearDimensions()
  }
})

// 加载维度数据
const loadDimensions = async (tableName: string) => {
  loading.value = true
  try {
    await dimensionsStore.fetchDimensions(tableName)
  } catch (error) {
    console.error('加载维度失败:', error)
  } finally {
    loading.value = false
  }
}

// 检查维度是否已选择
const isSelected = (dimensionName: string) => {
  return selectedDimensions.value.includes(dimensionName)
}

// 处理维度点击
const handleDimensionClick = (dimensionName: string) => {
  if (isSelected(dimensionName)) {
    // 取消选择
    handleDimensionDeselect(dimensionName)
  } else {
    // 选择维度
    const newSelected = [...selectedDimensions.value, dimensionName]
    selectedDimensions.value = newSelected
  }
}

// 处理维度取消选择
const handleDimensionDeselect = (dimensionName: string) => {
  const newSelected = selectedDimensions.value.filter(d => d !== dimensionName)
  selectedDimensions.value = newSelected
}

// 初始化
onMounted(() => {
  if (props.tableName) {
    loadDimensions(props.tableName)
  }
})
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.dimension-selector {
  .no-table-hint {
    margin-bottom: $spacing-md;
  }
  
  .search-bar {
    margin-bottom: $spacing-md;
  }
  
  .loading-container,
  .empty-container {
    padding: $spacing-lg;
  }
  
  .dimensions-container {
    .dimension-item {
      padding: $spacing-md;
      border: 1px solid $border-color-light;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-sm;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: $warning-color;
        box-shadow: 0 2px 8px rgba($warning-color, 0.1);
      }
      
      &.selected {
        border-color: $warning-color;
        background-color: lighten($warning-color, 45%);
      }
      
      .dimension-header {
        margin-bottom: $spacing-xs;
        
        .dimension-name {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
          
          .name {
            font-weight: 500;
            color: $text-primary;
          }
        }
      }
      
      .dimension-description {
        color: $text-secondary;
        font-size: $font-size-sm;
        margin-bottom: $spacing-xs;
        line-height: 1.4;
      }
      
      .dimension-info {
        display: flex;
        gap: $spacing-xs;
        
        .data-type,
        .nullable {
          font-size: $font-size-xs;
          color: $text-secondary;
          background: $bg-color-light;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }
  
  .selected-dimensions {
    margin-top: $spacing-lg;
    padding: $spacing-md;
    background: $bg-color-light;
    border-radius: $border-radius-md;
    
    .section-title {
      font-weight: 500;
      margin-bottom: $spacing-sm;
      color: $text-primary;
    }
    
    .dimension-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
    }
  }
}
</style>
