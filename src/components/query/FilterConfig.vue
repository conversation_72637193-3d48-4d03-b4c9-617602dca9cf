<template>
  <div class="filter-config">
    <div v-if="!tableName" class="no-table-hint">
      <el-alert
        title="请先选择数据表"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else>
      <!-- 添加过滤条件按钮 -->
      <div class="filter-header">
        <el-button
          type="primary"
          :icon="Plus"
          @click="addFilter"
        >
          添加过滤条件
        </el-button>
      </div>

      <!-- 过滤条件列表 -->
      <div v-if="filters.length === 0" class="empty-filters">
        <el-empty description="暂无过滤条件" />
      </div>

      <div v-else class="filters-list">
        <div
          v-for="(filter, index) in filters"
          :key="filter.id"
          class="filter-item"
        >
          <!-- 逻辑操作符 -->
          <div v-if="index > 0" class="logical-operator">
            <el-select
              v-model="filter.logicalOperator"
              style="width: 80px"
              @change="updateFilter(filter.id, { logicalOperator: filter.logicalOperator })"
            >
              <el-option label="AND" value="AND" />
              <el-option label="OR" value="OR" />
            </el-select>
          </div>

          <!-- 过滤条件配置 -->
          <el-card class="filter-card">
            <div class="filter-config-row">
              <!-- 字段选择 -->
              <div class="field-select">
                <label>字段</label>
                <el-select
                  v-model="filter.field"
                  placeholder="选择字段"
                  filterable
                  @change="updateFilter(filter.id, { field: filter.field })"
                >
                  <el-option-group label="维度">
                    <el-option
                      v-for="dimension in availableDimensions"
                      :key="dimension.name"
                      :label="dimension.name"
                      :value="dimension.name"
                    />
                  </el-option-group>
                  <el-option-group label="指标">
                    <el-option
                      v-for="metric in availableMetrics"
                      :key="metric.name"
                      :label="metric.name"
                      :value="metric.name"
                    />
                  </el-option-group>
                </el-select>
              </div>

              <!-- 操作符选择 -->
              <div class="operator-select">
                <label>操作符</label>
                <el-select
                  v-model="filter.operator"
                  placeholder="选择操作符"
                  @change="updateFilter(filter.id, { operator: filter.operator })"
                >
                  <el-option label="等于" value="eq" />
                  <el-option label="不等于" value="ne" />
                  <el-option label="大于" value="gt" />
                  <el-option label="大于等于" value="gte" />
                  <el-option label="小于" value="lt" />
                  <el-option label="小于等于" value="lte" />
                  <el-option label="包含" value="in" />
                  <el-option label="不包含" value="not_in" />
                  <el-option label="模糊匹配" value="like" />
                  <el-option label="不匹配" value="not_like" />
                  <el-option label="为空" value="is_null" />
                  <el-option label="不为空" value="is_not_null" />
                </el-select>
              </div>

              <!-- 值输入 -->
              <div class="value-input">
                <label>值</label>
                <FilterValueInput
                  v-model="filter.value"
                  :operator="filter.operator"
                  :field-type="getFieldType(filter.field)"
                  @change="updateFilter(filter.id, { value: filter.value })"
                />
              </div>

              <!-- 删除按钮 -->
              <div class="filter-actions">
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  size="small"
                  @click="removeFilter(filter.id)"
                />
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 过滤条件预览 -->
      <div v-if="filters.length > 0" class="filter-preview">
        <div class="section-title">过滤条件预览</div>
        <div class="preview-content">
          <code>{{ generateFilterPreview() }}</code>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { useDimensionsStore } from '@/stores/dimensions'
import { useMetricsStore } from '@/stores/metrics'
import FilterValueInput from './FilterValueInput.vue'
import type { FilterCondition } from '@/types/query'
import type { Dimension, BaseMetric, DerivedMetric } from '@/types/api'

interface Props {
  modelValue: FilterCondition[]
  tableName?: string
}

interface Emits {
  (e: 'update:modelValue', value: FilterCondition[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dimensionsStore = useDimensionsStore()
const metricsStore = useMetricsStore()

// 计算属性
const filters = computed({
  get: () => Array.isArray(props.modelValue) ? props.modelValue : [],
  set: (value) => emit('update:modelValue', value)
})

const availableDimensions = computed(() => dimensionsStore.dimensions)
const availableMetrics = computed(() => [
  ...metricsStore.baseMetrics,
  ...metricsStore.derivedMetrics
])

// 监听表名变化
watch(() => props.tableName, async (newTableName) => {
  if (newTableName) {
    await loadFieldsData(newTableName)
  }
})

// 加载字段数据
const loadFieldsData = async (tableName: string) => {
  try {
    await Promise.all([
      dimensionsStore.fetchDimensions(tableName),
      metricsStore.fetchBaseMetrics(tableName),
      metricsStore.fetchDerivedMetrics(tableName)
    ])
  } catch (error) {
    console.error('加载字段数据失败:', error)
  }
}

// 添加过滤条件
const addFilter = () => {
  const newFilter: FilterCondition = {
    id: Date.now().toString(),
    field: '',
    operator: 'eq',
    value: '',
    logicalOperator: filters.value.length > 0 ? 'AND' : undefined
  }
  
  const newFilters = [...filters.value, newFilter]
  filters.value = newFilters
}

// 更新过滤条件
const updateFilter = (filterId: string, updates: Partial<FilterCondition>) => {
  const index = filters.value.findIndex(f => f.id === filterId)
  if (index !== -1) {
    const updatedFilter = { ...filters.value[index], ...updates }
    const newFilters = [...filters.value]
    newFilters[index] = updatedFilter
    filters.value = newFilters
  }
}

// 删除过滤条件
const removeFilter = (filterId: string) => {
  const newFilters = filters.value.filter(f => f.id !== filterId)
  filters.value = newFilters
}

// 获取字段类型
const getFieldType = (fieldName: string) => {
  // 查找维度
  const dimension = availableDimensions.value.find(d => d.name === fieldName)
  if (dimension) {
    return dimension.dataType || 'string'
  }
  
  // 查找指标
  const metric = availableMetrics.value.find(m => m.name === fieldName)
  if (metric && 'dataType' in metric) {
    return metric.dataType || 'number'
  }
  
  return 'string'
}

// 生成过滤条件预览
const generateFilterPreview = () => {
  if (filters.value.length === 0) return ''
  
  return filters.value.map((filter, index) => {
    let condition = ''
    
    if (index > 0 && filter.logicalOperator) {
      condition += `${filter.logicalOperator} `
    }
    
    condition += `${filter.field} ${getOperatorText(filter.operator)}`
    
    if (!['is_null', 'is_not_null'].includes(filter.operator)) {
      condition += ` ${formatValue(filter.value, filter.operator)}`
    }
    
    return condition
  }).join(' ')
}

// 获取操作符文本
const getOperatorText = (operator: string) => {
  const operatorMap: Record<string, string> = {
    eq: '=',
    ne: '!=',
    gt: '>',
    gte: '>=',
    lt: '<',
    lte: '<=',
    in: 'IN',
    not_in: 'NOT IN',
    like: 'LIKE',
    not_like: 'NOT LIKE',
    is_null: 'IS NULL',
    is_not_null: 'IS NOT NULL'
  }
  return operatorMap[operator] || operator
}

// 格式化值
const formatValue = (value: any, operator: string) => {
  if (['in', 'not_in'].includes(operator)) {
    if (Array.isArray(value)) {
      return `(${value.map(v => `'${v}'`).join(', ')})`
    }
    return `('${value}')`
  }
  
  if (typeof value === 'string') {
    return `'${value}'`
  }
  
  return value
}

// 初始化
onMounted(() => {
  if (props.tableName) {
    loadFieldsData(props.tableName)
  }
})
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.filter-config {
  .no-table-hint {
    margin-bottom: $spacing-md;
  }
  
  .filter-header {
    margin-bottom: $spacing-md;
  }
  
  .empty-filters {
    padding: $spacing-lg;
  }
  
  .filters-list {
    .filter-item {
      margin-bottom: $spacing-md;
      
      .logical-operator {
        text-align: center;
        margin-bottom: $spacing-sm;
      }
      
      .filter-card {
        .filter-config-row {
          display: flex;
          align-items: end;
          gap: $spacing-md;
          
          > div {
            flex: 1;
            
            label {
              display: block;
              margin-bottom: $spacing-xs;
              font-size: $font-size-sm;
              color: $text-secondary;
            }
          }
          
          .filter-actions {
            flex: 0 0 auto;
          }
        }
      }
    }
  }
  
  .filter-preview {
    margin-top: $spacing-lg;
    padding: $spacing-md;
    background: $bg-color-light;
    border-radius: $border-radius;
    
    .section-title {
      font-weight: 500;
      margin-bottom: $spacing-sm;
      color: $text-color-primary;
    }
    
    .preview-content {
      code {
        background: white;
        padding: $spacing-sm;
        border-radius: $border-radius;
        display: block;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: $font-size-sm;
        line-height: 1.4;
        border: 1px solid $border-color-light;
      }
    }
  }
}
</style>
