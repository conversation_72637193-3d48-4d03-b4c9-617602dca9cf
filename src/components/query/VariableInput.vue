<template>
  <div class="variable-input">
    <div class="variable-header">
      <h3>查询变量</h3>
      <el-button size="small" @click="addVariable">
        <el-icon><Plus /></el-icon>
        添加变量
      </el-button>
    </div>

    <div v-if="variables.length === 0" class="empty-state">
      <el-empty description="暂无查询变量" />
    </div>

    <div v-else class="variable-list">
      <div 
        v-for="(variable, index) in variables" 
        :key="variable.id || index"
        class="variable-item"
      >
        <div class="variable-info">
          <div class="variable-name">
            <el-tag size="small">{{ variable.name }}</el-tag>
            <span class="variable-type">{{ getVariableTypeLabel(variable.type) }}</span>
          </div>
          <div class="variable-description">{{ variable.description }}</div>
        </div>

        <div class="variable-input-field">
          <!-- 字符串类型 -->
          <el-input
            v-if="variable.type === 'string'"
            v-model="variable.value"
            :placeholder="variable.defaultValue || '请输入值'"
            @input="updateVariable(index, $event)"
          />

          <!-- 数字类型 -->
          <el-input-number
            v-else-if="variable.type === 'number'"
            v-model="variable.value"
            :placeholder="variable.defaultValue || '请输入数字'"
            style="width: 100%"
            @change="updateVariable(index, $event)"
          />

          <!-- 日期类型 -->
          <el-date-picker
            v-else-if="variable.type === 'date'"
            v-model="variable.value"
            type="date"
            :placeholder="variable.defaultValue || '请选择日期'"
            style="width: 100%"
            @change="updateVariable(index, $event)"
          />

          <!-- 日期时间类型 -->
          <el-date-picker
            v-else-if="variable.type === 'datetime'"
            v-model="variable.value"
            type="datetime"
            :placeholder="variable.defaultValue || '请选择日期时间'"
            style="width: 100%"
            @change="updateVariable(index, $event)"
          />

          <!-- 布尔类型 -->
          <el-switch
            v-else-if="variable.type === 'boolean'"
            v-model="variable.value"
            @change="updateVariable(index, $event)"
          />

          <!-- 列表类型 -->
          <el-select
            v-else-if="variable.type === 'list'"
            v-model="variable.value"
            multiple
            :placeholder="variable.defaultValue || '请选择值'"
            style="width: 100%"
            @change="updateVariable(index, $event)"
          >
            <el-option
              v-for="option in getVariableOptions(variable)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <!-- 默认字符串输入 -->
          <el-input
            v-else
            v-model="variable.value"
            :placeholder="variable.defaultValue || '请输入值'"
            @input="updateVariable(index, $event)"
          />
        </div>

        <div class="variable-actions">
          <el-button 
            size="small" 
            type="danger" 
            text 
            @click="removeVariable(index)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加变量对话框 -->
    <el-dialog
      v-model="addVariableDialogVisible"
      title="添加查询变量"
      width="500px"
    >
      <el-form
        ref="variableFormRef"
        :model="newVariable"
        :rules="variableRules"
        label-width="100px"
      >
        <el-form-item label="变量名" prop="name">
          <el-input
            v-model="newVariable.name"
            placeholder="请输入变量名（英文）"
          />
        </el-form-item>

        <el-form-item label="变量类型" prop="type">
          <el-select v-model="newVariable.type" style="width: 100%">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="日期" value="date" />
            <el-option label="日期时间" value="datetime" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="列表" value="list" />
          </el-select>
        </el-form-item>

        <el-form-item label="默认值" prop="defaultValue">
          <el-input
            v-model="newVariable.defaultValue"
            placeholder="请输入默认值"
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="newVariable.description"
            type="textarea"
            :rows="2"
            placeholder="请输入变量描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addVariableDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddVariable">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

interface QueryVariable {
  id?: number
  name: string
  type: string
  value: any
  defaultValue?: string
  description?: string
  options?: Array<{ label: string; value: any }>
}

interface Props {
  modelValue: QueryVariable[]
}

interface Emits {
  (e: 'update:modelValue', value: QueryVariable[]): void
  (e: 'change', value: QueryVariable[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const variableFormRef = ref<FormInstance>()
const addVariableDialogVisible = ref(false)

// 计算属性
const variables = computed({
  get: () => Array.isArray(props.modelValue) ? props.modelValue : [],
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 新变量表单
const newVariable = reactive<Partial<QueryVariable>>({
  name: '',
  type: 'string',
  defaultValue: '',
  description: ''
})

// 表单验证规则
const variableRules: FormRules = {
  name: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '变量名必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择变量类型', trigger: 'change' }
  ]
}

// 获取变量类型标签
const getVariableTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    string: '字符串',
    number: '数字',
    date: '日期',
    datetime: '日期时间',
    boolean: '布尔值',
    list: '列表'
  }
  return typeMap[type] || type
}

// 获取变量选项（用于列表类型）
const getVariableOptions = (variable: QueryVariable) => {
  return variable.options || []
}

// 更新变量值
const updateVariable = (index: number, value: any) => {
  const newVariables = [...variables.value]
  newVariables[index].value = value
  variables.value = newVariables
}

// 添加变量
const addVariable = () => {
  addVariableDialogVisible.value = true
}

// 确认添加变量
const confirmAddVariable = async () => {
  if (!variableFormRef.value) return

  try {
    const valid = await variableFormRef.value.validate()
    if (!valid) return

    const variable: QueryVariable = {
      id: Date.now(),
      name: newVariable.name!,
      type: newVariable.type!,
      value: newVariable.defaultValue || '',
      defaultValue: newVariable.defaultValue,
      description: newVariable.description
    }

    variables.value = [...variables.value, variable]
    
    // 重置表单
    Object.assign(newVariable, {
      name: '',
      type: 'string',
      defaultValue: '',
      description: ''
    })
    
    addVariableDialogVisible.value = false
    ElMessage.success('变量添加成功')
  } catch (error) {
    console.error('Add variable error:', error)
  }
}

// 移除变量
const removeVariable = (index: number) => {
  const newVariables = [...variables.value]
  newVariables.splice(index, 1)
  variables.value = newVariables
  ElMessage.success('变量已移除')
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.variable-input {
  .variable-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    
    h3 {
      margin: 0;
      color: $text-primary;
    }
  }

  .empty-state {
    padding: $spacing-lg 0;
  }

  .variable-list {
    .variable-item {
      display: flex;
      align-items: flex-start;
      gap: $spacing-md;
      padding: $spacing-md;
      border: 1px solid $border-color;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-sm;

      .variable-info {
        flex: 1;
        min-width: 200px;

        .variable-name {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
          margin-bottom: $spacing-xs;

          .variable-type {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }

        .variable-description {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .variable-input-field {
        flex: 2;
        min-width: 200px;
      }

      .variable-actions {
        flex-shrink: 0;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-sm;
  }
}
</style>
