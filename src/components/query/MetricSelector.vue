<template>
  <div class="metric-selector">
    <div v-if="!tableName" class="no-table-hint">
      <el-alert
        title="请先选择数据表"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else>
      <!-- 搜索框 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索指标..."
          clearable
          :prefix-icon="Search"
        />
      </div>

      <!-- 指标类型标签页 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="基础指标" name="base">
          <MetricList
            :metrics="filteredBaseMetrics"
            :selected="selectedMetrics"
            :loading="loading"
            @select="handleMetricSelect"
            @deselect="handleMetricDeselect"
          />
        </el-tab-pane>
        
        <el-tab-pane label="衍生指标" name="derived">
          <MetricList
            :metrics="filteredDerivedMetrics"
            :selected="selectedMetrics"
            :loading="loading"
            @select="handleMetricSelect"
            @deselect="handleMetricDeselect"
          />
        </el-tab-pane>
      </el-tabs>

      <!-- 已选指标 -->
      <div v-if="selectedMetrics.length > 0" class="selected-metrics">
        <div class="section-title">已选指标 ({{ selectedMetrics.length }})</div>
        <div class="metric-tags">
          <el-tag
            v-for="metric in selectedMetrics"
            :key="metric.name"
            :type="metric.type === 'base' ? 'primary' : 'success'"
            closable
            @close="handleMetricDeselect(metric)"
          >
            {{ metric.displayName || metric.name }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useMetricsStore } from '@/stores/metrics'
import MetricList from './MetricList.vue'
import type { MetricSelection } from '@/types/query'
import type { BaseMetric, DerivedMetric } from '@/types/api'

interface Props {
  modelValue: MetricSelection[]
  tableName?: string
}

interface Emits {
  (e: 'update:modelValue', value: MetricSelection[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const metricsStore = useMetricsStore()

const searchKeyword = ref('')
const activeTab = ref('base')
const loading = ref(false)

// 计算属性
const baseMetrics = computed(() => metricsStore.baseMetrics)
const derivedMetrics = computed(() => metricsStore.derivedMetrics)

const filteredBaseMetrics = computed(() => {
  if (!searchKeyword.value) return baseMetrics.value
  return baseMetrics.value.filter(metric => 
    metric.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (metric.description && metric.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

const filteredDerivedMetrics = computed(() => {
  if (!searchKeyword.value) return derivedMetrics.value
  return derivedMetrics.value.filter(metric => 
    metric.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (metric.description && metric.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

const selectedMetrics = computed({
  get: () => Array.isArray(props.modelValue) ? props.modelValue : [],
  set: (value) => emit('update:modelValue', value)
})

// 监听表名变化
watch(() => props.tableName, async (newTableName) => {
  if (newTableName) {
    await loadMetrics(newTableName)
  } else {
    // 清空指标数据
    metricsStore.clearMetrics()
  }
})

// 加载指标数据
const loadMetrics = async (tableName: string) => {
  loading.value = true
  try {
    await Promise.all([
      metricsStore.fetchBaseMetrics(tableName),
      metricsStore.fetchDerivedMetrics(tableName)
    ])
  } catch (error) {
    console.error('加载指标失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理指标选择
const handleMetricSelect = (metric: BaseMetric | DerivedMetric) => {
  const metricSelection: MetricSelection = {
    name: metric.name,
    displayName: metric.name,
    type: 'expression' in metric ? 'derived' : 'base',
    expression: 'expression' in metric ? metric.expression : undefined
  }
  
  const newSelected = [...selectedMetrics.value, metricSelection]
  selectedMetrics.value = newSelected
}

// 处理指标取消选择
const handleMetricDeselect = (metric: MetricSelection) => {
  const newSelected = selectedMetrics.value.filter(m => m.name !== metric.name)
  selectedMetrics.value = newSelected
}

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

// 初始化
onMounted(() => {
  if (props.tableName) {
    loadMetrics(props.tableName)
  }
})
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.metric-selector {
  .no-table-hint {
    margin-bottom: $spacing-md;
  }
  
  .search-bar {
    margin-bottom: $spacing-md;
  }
  
  .selected-metrics {
    margin-top: $spacing-lg;
    padding: $spacing-md;
    background: $bg-color-light;
    border-radius: $border-radius-md;
    
    .section-title {
      font-weight: 500;
      margin-bottom: $spacing-sm;
      color: $text-primary;
    }
    
    .metric-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
    }
  }
}
</style>
