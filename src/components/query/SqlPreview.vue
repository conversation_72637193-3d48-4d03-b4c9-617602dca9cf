<template>
  <div class="sql-preview">
    <div class="preview-header">
      <h3>SQL预览</h3>
      <div class="header-actions">
        <el-button size="small" :loading="loading" @click="refreshPreview">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="copyToClipboard">
          <el-icon>
            <CopyDocument />
          </el-icon>
          复制
        </el-button>
        <el-button size="small" type="primary" :loading="executing" @click="executeQuery">
          <el-icon>
            <CaretRight />
          </el-icon>
          执行查询
        </el-button>
      </div>
    </div>

    <div class="preview-content">
      <!-- SQL编辑器 -->
      <div v-loading="loading" element-loading-text="正在生成SQL...">
        <SqlEditor v-model="generatedSql" :readonly="true" :show-actions="false" height="300px" />
      </div>

      <!-- SQL验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <div class="validation-header">
          <h4>SQL验证结果</h4>
          <el-tag :type="validationResult.valid ? 'success' : 'danger'" size="small">
            {{ validationResult.valid ? '验证通过' : '验证失败' }}
          </el-tag>
        </div>

        <div v-if="!validationResult.valid && validationResult.errors && validationResult.errors.length > 0"
          class="validation-errors">
          <h5>错误信息:</h5>
          <ul>
            <li v-for="error in validationResult.errors" :key="error" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>

        <div v-if="validationResult.warnings && validationResult.warnings.length > 0" class="validation-warnings">
          <h5>警告信息:</h5>
          <ul>
            <li v-for="warning in validationResult.warnings" :key="warning" class="warning-item">
              {{ warning }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 变量替换信息 -->
      <div v-if="variableReplacements.length > 0" class="variable-info">
        <h4>变量替换</h4>
        <div class="replacement-list">
          <div v-for="replacement in variableReplacements" :key="replacement.name" class="replacement-item">
            <span class="variable-name">${{ replacement.name }}</span>
            <el-icon class="arrow">
              <ArrowRight />
            </el-icon>
            <span class="variable-value">{{ replacement.value }}</span>
          </div>
        </div>
      </div>

      <!-- 查询统计信息 -->
      <div class="query-stats">
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="预估行数">
            {{ queryStats.estimatedRows || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="查询复杂度">
            <el-tag :type="getComplexityType(queryStats.complexity)">
              {{ getComplexityLabel(queryStats.complexity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="预估执行时间">
            {{ queryStats.estimatedTime || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 执行结果 -->
    <div v-if="executionResult" class="execution-result">
      <div class="result-header">
        <h4>执行结果</h4>
        <el-tag :type="executionResult.success ? 'success' : 'danger'" size="small">
          {{ executionResult.success ? '成功' : '失败' }}
        </el-tag>
      </div>

      <div v-if="executionResult.success" class="result-content">
        <!-- 结果表格 -->
        <el-table :data="executionResult.data" :max-height="400" stripe border>
          <el-table-column v-for="column in executionResult.columns" :key="column.name" :prop="column.name"
            :label="column.label" :width="column.width" show-overflow-tooltip />
        </el-table>

        <!-- 执行统计 -->
        <div class="execution-stats">
          <el-descriptions :column="4" size="small">
            <el-descriptions-item label="返回行数">
              {{ executionResult.rowCount }}
            </el-descriptions-item>
            <el-descriptions-item label="执行时间">
              {{ executionResult.executionTime }}ms
            </el-descriptions-item>
            <el-descriptions-item label="数据大小">
              {{ formatBytes(executionResult.dataSize) }}
            </el-descriptions-item>
            <el-descriptions-item label="查询ID">
              {{ executionResult.queryId }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div v-else class="error-content">
        <el-alert type="error" :title="executionResult.error" :description="executionResult.details" show-icon />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, CopyDocument, CaretRight, ArrowRight } from '@element-plus/icons-vue'
import SqlEditor from '@/components/common/SqlEditor.vue'
import { queryApi } from '@/api/query'
import type { QueryPreviewRequest, QueryValidateRequest } from '@/types/query'

interface QueryConfig {
  tableName?: string
  dimensions: string[]
  metrics: any[]
  filters: any[]
  variables: any[]
  groupBy?: string[]
  orderBy?: string[]
  limit?: number
}

interface VariableReplacement {
  name: string
  value: string
  type: string
}

interface QueryStats {
  estimatedRows?: number
  complexity?: 'low' | 'medium' | 'high'
  estimatedTime?: string
}

interface ExecutionResult {
  success: boolean
  data?: any[]
  columns?: Array<{ name: string; label: string; width?: number }>
  rowCount?: number
  executionTime?: number
  dataSize?: number
  queryId?: string
  error?: string
  details?: string
}

interface Props {
  queryConfig: QueryConfig
}

interface Emits {
  (e: 'execute', sql: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const executing = ref(false)
const loading = ref(false)
const generatedSql = ref('')
const executionResult = ref<ExecutionResult | null>(null)
const validationResult = ref<{ valid: boolean; errors?: string[]; warnings?: string[] } | null>(null)

// 计算属性
const variableReplacements = computed<VariableReplacement[]>(() => {
  return props.queryConfig.variables?.map(variable => ({
    name: variable.name,
    value: String(variable.value || variable.defaultValue || ''),
    type: variable.type
  })) || []
})

const queryStats = computed<QueryStats>(() => {
  // 从API响应中获取查询统计信息，如果没有则使用默认值
  if (validationResult.value) {
    return {
      estimatedRows: 0, // 将从API获取
      complexity: 'low', // 将从API获取
      estimatedTime: '<1s' // 将从API获取
    }
  }

  // 简单的查询复杂度评估（作为后备）
  const dimensionCount = props.queryConfig.dimensions?.length || 0
  const metricCount = props.queryConfig.metrics?.length || 0
  const filterCount = props.queryConfig.filters?.length || 0

  const totalComplexity = dimensionCount + metricCount + filterCount

  let complexity: 'low' | 'medium' | 'high' = 'low'
  if (totalComplexity > 10) complexity = 'high'
  else if (totalComplexity > 5) complexity = 'medium'

  return {
    estimatedRows: Math.floor(Math.random() * 1000000) + 1000, // 模拟数据
    complexity,
    estimatedTime: complexity === 'high' ? '5-10s' : complexity === 'medium' ? '1-5s' : '<1s'
  }
})

// 监听查询配置变化，自动生成SQL
watch(() => props.queryConfig, generateSql, { deep: true, immediate: true })

// 生成SQL - 使用后台API
async function generateSql() {
  const config = props.queryConfig

  if (!config.tableName || (!config.dimensions.length && !config.metrics.length)) {
    generatedSql.value = '-- 请选择表和字段'
    return
  }

  try {
    loading.value = true

    // 准备API请求数据
    const requestData: QueryPreviewRequest = {
      tableName: config.tableName,
      metrics: config.metrics.map(metric => typeof metric === 'string' ? metric : metric.name),
      dimensions: config.dimensions,
      filters: config.filters
    }

    // 调用预览API
    const response = await queryApi.preview(requestData)
    generatedSql.value = response.sql

    // 如果有警告，显示给用户
    if (response.warnings && response.warnings.length > 0) {
      response.warnings.forEach(warning => {
        ElMessage.warning(warning)
      })
    }

    // 自动验证生成的SQL
    await validateSql(response.sql)

  } catch (error) {
    console.error('生成SQL失败:', error)
    generatedSql.value = '-- SQL生成失败，请检查配置'
    ElMessage.error('SQL生成失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    loading.value = false
  }
}

// 验证SQL
async function validateSql(sql: string) {
  if (!sql || sql.trim().startsWith('--')) {
    return
  }

  try {
    const validateRequest: QueryValidateRequest = { sql }
    const result = await queryApi.validate(validateRequest)
    validationResult.value = result

    // 显示验证错误
    if (!result.valid && result.errors && result.errors.length > 0) {
      result.errors.forEach(error => {
        ElMessage.error(`SQL验证错误: ${error}`)
      })
    }

    // 显示验证警告
    if (result.warnings && result.warnings.length > 0) {
      result.warnings.forEach(warning => {
        ElMessage.warning(`SQL验证警告: ${warning}`)
      })
    }

  } catch (error) {
    console.error('SQL验证失败:', error)
  }
}

// 刷新预览
async function refreshPreview() {
  await generateSql()
  ElMessage.success('SQL预览已刷新')
}

// 复制到剪贴板
async function copyToClipboard() {
  try {
    await navigator.clipboard.writeText(generatedSql.value)
    ElMessage.success('SQL已复制到剪贴板')
  } catch (error) {
    console.error('Copy failed:', error)
    ElMessage.error('复制失败')
  }
}

// 执行查询
async function executeQuery() {
  if (!generatedSql.value.trim()) {
    ElMessage.warning('请先生成SQL')
    return
  }

  executing.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟执行结果
    executionResult.value = {
      success: true,
      data: [
        { date: '2024-01-01', pv: 1000, uv: 800 },
        { date: '2024-01-02', pv: 1200, uv: 900 },
        { date: '2024-01-03', pv: 1100, uv: 850 }
      ],
      columns: [
        { name: 'date', label: '日期', width: 120 },
        { name: 'pv', label: '页面浏览量', width: 120 },
        { name: 'uv', label: '独立访客', width: 120 }
      ],
      rowCount: 3,
      executionTime: 1234,
      dataSize: 1024,
      queryId: 'query_' + Date.now()
    }

    emit('execute', generatedSql.value)
    ElMessage.success('查询执行成功')
  } catch (error) {
    console.error('Execute query error:', error)
    executionResult.value = {
      success: false,
      error: '查询执行失败',
      details: error instanceof Error ? error.message : '未知错误'
    }
    ElMessage.error('查询执行失败')
  } finally {
    executing.value = false
  }
}

// 获取复杂度类型
function getComplexityType(complexity?: string) {
  const typeMap: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return typeMap[complexity || 'low']
}

// 获取复杂度标签
function getComplexityLabel(complexity?: string) {
  const labelMap: Record<string, string> = {
    low: '简单',
    medium: '中等',
    high: '复杂'
  }
  return labelMap[complexity || 'low']
}

// 格式化字节数
function formatBytes(bytes?: number) {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.sql-preview {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    h3 {
      margin: 0;
      color: $text-primary;
    }

    .header-actions {
      display: flex;
      gap: $spacing-xs;
    }
  }

  .preview-content {
    .variable-info {
      margin: $spacing-md 0;
      padding: $spacing-md;
      background: $bg-color-light;
      border-radius: $border-radius-md;

      h4 {
        margin: 0 0 $spacing-sm 0;
        color: $text-primary;
        font-size: $font-size-sm;
      }

      .replacement-list {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-sm;

        .replacement-item {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
          padding: $spacing-xs $spacing-sm;
          background: white;
          border-radius: $border-radius-md;
          font-size: $font-size-sm;

          .variable-name {
            color: $primary-color;
            font-family: monospace;
          }

          .arrow {
            color: $text-secondary;
          }

          .variable-value {
            color: $text-primary;
            font-weight: 500;
          }
        }
      }
    }

    .validation-result {
      margin: $spacing-md 0;
      padding: $spacing-md;
      background: $bg-color-light;
      border-radius: $border-radius-md;

      .validation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-sm;

        h4 {
          margin: 0;
          color: $text-primary;
          font-size: $font-size-sm;
        }
      }

      .validation-errors,
      .validation-warnings {
        margin-top: $spacing-sm;

        h5 {
          margin: 0 0 $spacing-xs 0;
          font-size: $font-size-xs;
          color: $text-secondary;
        }

        ul {
          margin: 0;
          padding-left: $spacing-md;

          .error-item {
            color: $danger-color;
            font-size: $font-size-xs;
            margin-bottom: $spacing-xs;
          }

          .warning-item {
            color: $warning-color;
            font-size: $font-size-xs;
            margin-bottom: $spacing-xs;
          }
        }
      }
    }

    .query-stats {
      margin-top: $spacing-md;
    }
  }

  .execution-result {
    margin-top: $spacing-lg;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-color;

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;

      h4 {
        margin: 0;
        color: $text-primary;
      }
    }

    .result-content {
      .execution-stats {
        margin-top: $spacing-md;
      }
    }

    .error-content {
      margin-top: $spacing-md;
    }
  }
}
</style>
